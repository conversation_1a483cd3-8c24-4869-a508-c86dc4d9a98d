import { ofetch } from "ofetch";
import type { LCAuthToken, LCAuthConfig } from "./types";
import { universalStorage } from "./utils";

/**
 * Firebase Auth API Response Types
 */
export interface FirebaseAuthResponse {
  idToken: string;
  refreshToken: string;
  expiresIn: string;
  localId: string;
  email?: string;
}

export interface FirebaseRefreshResponse {
  access_token: string;
  expires_in: string;
  token_type: string;
  refresh_token: string;
  id_token: string;
  user_id: string;
  project_id: string;
}

export interface FirebaseUserInfo {
  localId: string;
  email?: string;
  emailVerified?: boolean;
  displayName?: string;
  photoUrl?: string;
  passwordHash?: string;
  passwordUpdatedAt?: number;
  validSince?: string;
  disabled?: boolean;
  lastLoginAt?: string;
  createdAt?: string;
  customAuth?: boolean;
}

export interface FirebaseUserLookupResponse {
  users: FirebaseUserInfo[];
}

/**
 * Enhanced authentication utilities for Firestore REST API
 * Integrates with LC backend authentication flow
 */

/**
 * LC Backend Authentication Manager
 * Handles custom tokens from the LC backend and Firebase Auth integration
 */
export class LCFirebaseAuth {
  private config: LCAuthConfig;
  private currentToken: string | null = null;
  private tokenExpiry: number | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  constructor(config: LCAuthConfig) {
    this.config = config;
  }

  /**
   * Set custom token from LC backend
   */
  public setCustomToken(token: string): void {
    this.config.customToken = token;
    this.currentToken = null; // Reset current token to force refresh
  }

  /**
   * Exchange custom token for Firebase ID token
   */
  public async exchangeCustomToken(customToken?: string): Promise<LCAuthToken> {
    const token = customToken || this.config.customToken;
    
    if (!token) {
      throw new Error("No custom token available for exchange");
    }

    const response = await ofetch<FirebaseAuthResponse>(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${this.config.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          token,
          returnSecureToken: true,
        },
      }
    );

    const authResult: LCAuthToken = {
      idToken: response.idToken,
      refreshToken: response.refreshToken,
      expiresIn: response.expiresIn,
      localId: response.localId,
      ...(response.email && { email: response.email }),
    };

    // Store token and set up auto-refresh
    this.currentToken = authResult.idToken;
    this.tokenExpiry = Date.now() + (parseInt(authResult.expiresIn || "3600") * 1000);
    this.setupTokenRefresh(authResult.refreshToken);

    // Notify callback if provided
    if (this.config.onTokenRefresh) {
      this.config.onTokenRefresh(authResult.idToken);
    }

    return authResult;
  }

  /**
   * Get current valid Firebase ID token
   */
  public async getIdToken(): Promise<string | null> {
    // If we have a getAuthToken function from the config, use it
    if (this.config.getAuthToken) {
      try {
        return await this.config.getAuthToken();
      } catch (error) {
        console.warn("Failed to get auth token from config function:", error);
      }
    }

    // Check if current token is still valid
    if (this.currentToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.currentToken;
    }

    // Try to exchange custom token if available
    if (this.config.customToken) {
      try {
        const authResult = await this.exchangeCustomToken();
        return authResult.idToken;
      } catch (error) {
        console.error("Failed to exchange custom token:", error);
        return null;
      }
    }

    return null;
  }

  /**
   * Refresh Firebase ID token using refresh token
   */
  public async refreshIdToken(refreshToken: string): Promise<LCAuthToken> {
    const response = await ofetch<FirebaseRefreshResponse>("https://securetoken.googleapis.com/v1/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      }),
      params: {
        key: this.config.apiKey,
      },
    });

    const authResult: LCAuthToken = {
      idToken: response.id_token,
      refreshToken: response.refresh_token,
      expiresIn: response.expires_in,
      localId: response.user_id,
    };

    // Update current token
    this.currentToken = authResult.idToken;
    this.tokenExpiry = Date.now() + (parseInt(authResult.expiresIn || "3600") * 1000);

    // Notify callback if provided
    if (this.config.onTokenRefresh) {
      this.config.onTokenRefresh(authResult.idToken);
    }

    return authResult;
  }

  /**
   * Set up automatic token refresh
   */
  private setupTokenRefresh(refreshToken?: string): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!refreshToken || !this.tokenExpiry) {
      return;
    }

    // Refresh token 5 minutes before expiry
    const refreshTime = this.tokenExpiry - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        try {
          await this.refreshIdToken(refreshToken);
        } catch (error) {
          console.error("Auto token refresh failed:", error);
        }
      }, refreshTime);
    }
  }

  /**
   * Clear authentication state
   */
  public clearAuth(): void {
    this.currentToken = null;
    this.tokenExpiry = null;
    delete this.config.customToken;
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Check if user is authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    const token = await this.getIdToken();
    return token !== null;
  }

  /**
   * Get user information from Firebase
   */
  public async getUserInfo(): Promise<FirebaseUserInfo | null> {
    const idToken = await this.getIdToken();
    
    if (!idToken) {
      throw new Error("No authentication token available");
    }

    const response = await ofetch<FirebaseUserLookupResponse>(
      `https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${this.config.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          idToken,
        },
      }
    );

    return response.users?.[0] || null;
  }
}

/**
 * Token manager for universal storage (works in both browser and server environments)
 */
export class TokenManager {
  private static readonly CUSTOM_TOKEN_KEY = "lc_custom_token";
  private static readonly ID_TOKEN_KEY = "firebase_id_token";
  private static readonly REFRESH_TOKEN_KEY = "firebase_refresh_token";
  private static readonly EXPIRY_KEY = "firebase_token_expiry";

  /**
   * Store custom token from LC backend
   */
  public static storeCustomToken(token: string): void {
    universalStorage.setItem(this.CUSTOM_TOKEN_KEY, token);
  }

  /**
   * Get stored custom token
   */
  public static getCustomToken(): string | null {
    return universalStorage.getItem(this.CUSTOM_TOKEN_KEY);
  }

  /**
   * Store Firebase authentication tokens
   */
  public static storeFirebaseTokens(authResponse: LCAuthToken): void {
    universalStorage.setItem(this.ID_TOKEN_KEY, authResponse.idToken);

    if (authResponse.refreshToken) {
      universalStorage.setItem(this.REFRESH_TOKEN_KEY, authResponse.refreshToken);
    }

    if (authResponse.expiresIn) {
      const expiryTime = Date.now() + (parseInt(authResponse.expiresIn) * 1000);
      universalStorage.setItem(this.EXPIRY_KEY, expiryTime.toString());
    }
  }

  /**
   * Get stored Firebase ID token
   */
  public static getIdToken(): string | null {
    return universalStorage.getItem(this.ID_TOKEN_KEY);
  }

  /**
   * Get stored refresh token
   */
  public static getRefreshToken(): string | null {
    return universalStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  /**
   * Check if Firebase ID token is expired
   */
  public static isTokenExpired(): boolean {
    const expiryTime = universalStorage.getItem(this.EXPIRY_KEY);
    if (expiryTime) {
      return Date.now() > parseInt(expiryTime);
    }
    return true;
  }

  /**
   * Clear all stored tokens
   */
  public static clearTokens(): void {
    universalStorage.removeItem(this.CUSTOM_TOKEN_KEY);
    universalStorage.removeItem(this.ID_TOKEN_KEY);
    universalStorage.removeItem(this.REFRESH_TOKEN_KEY);
    universalStorage.removeItem(this.EXPIRY_KEY);
  }

  /**
   * Get valid token with auto-refresh
   */
  public static async getValidToken(auth: LCFirebaseAuth): Promise<string | null> {
    // Try to get current token
    let token = this.getIdToken();
    
    // If token is expired, try to refresh
    if (!token || this.isTokenExpired()) {
      const refreshToken = this.getRefreshToken();
      
      if (refreshToken) {
        try {
          const refreshed = await auth.refreshIdToken(refreshToken);
          this.storeFirebaseTokens(refreshed);
          return refreshed.idToken;
        } catch (error) {
          console.error("Token refresh failed:", error);
          this.clearTokens();
          return null;
        }
      }
      
      // Try to exchange custom token
      const customToken = this.getCustomToken();
      if (customToken) {
        try {
          const exchanged = await auth.exchangeCustomToken(customToken);
          this.storeFirebaseTokens(exchanged);
          return exchanged.idToken;
        } catch (error) {
          console.error("Custom token exchange failed:", error);
          return null;
        }
      }
      
      return null;
    }
    
    return token;
  }
}
