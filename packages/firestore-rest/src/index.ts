// Main exports for the Firestore REST API library with LC Backend Integration

// Core classes
export { FirestoreClient } from "./client";
export { FirestoreOperations } from "./operations";
export { LCFirebaseAuth, TokenManager } from "./auth";

// Import types and classes for internal use
import type {
  LCAuthConfig,
  DocumentData,
  QueryOptions,
  WhereFilter,
} from "./types";
import { FirestoreClient } from "./client";
import { FirestoreOperations } from "./operations";
import { TokenManager } from "./auth";

// Types
export type {
  LCAuthConfig,
  LCAuthToken,
  FirestoreDocument,
  FirestoreValue,
  DocumentData,
  QueryOptions,
  WhereFilter,
  OrderBy,
  UseFirestoreResult,
  UseFirestoreListResult,
} from "./types";

// Export LCIntegrationConfig and utilities from lcIntegration
export type { LCIntegrationConfig } from "./lcIntegration";
export { createLCFirestore, createLCAuthTokenGetter, useLCFirestore } from "./lcIntegration";

// Utilities
export {
  toFirestoreValue,
  fromFirestoreValue,
  toFirestoreFields,
  fromFirestoreFields,
  extractDocumentId,
  buildDocumentPath,
  validateCollectionName,
  validateDocumentId,
  generateDocumentId,
  handleFirestoreError,
} from "./utils";

// React hooks
export {
  useDocument,
  useCollection,
  useQuery,
  usePagination,
  useRealtimeDocument,
  useSearch,
  useFirestoreCRUD,
  useFirestoreAuth,
} from "./hooks";

// Validation schemas
export {
  firestoreConfigSchema,
  lcAuthConfigSchema,
  whereFilterSchema,
  queryOptionsSchema,
  isFirestoreError,
} from "./types";

/**
 * Factory function to create a configured Firestore instance with LC Backend integration
 */
export const createFirestore = (config: {
  projectId: string;
  apiKey: string;
  customToken?: string;
  getAuthToken?: () => Promise<string | null>;
  onTokenRefresh?: (token: string) => void;
}) => {
  const authConfig: LCAuthConfig = {
    projectId: config.projectId,
    apiKey: config.apiKey,
    ...(config.customToken !== undefined && { customToken: config.customToken }),
    ...(config.getAuthToken !== undefined && { getAuthToken: config.getAuthToken }),
    ...(config.onTokenRefresh !== undefined && { onTokenRefresh: config.onTokenRefresh }),
  };

  const client = new FirestoreClient(authConfig);
  const operations = new FirestoreOperations(client);

  return {
    client,
    operations,
    auth: client.getAuth(),
    // Convenience methods
    collection: (name: string) => ({
      add: (data: DocumentData) => operations.add(name, data),
      set: (id: string, data: DocumentData) => operations.set(name, id, data),
      get: (id: string) => operations.get(name, id),
      update: (id: string, data: DocumentData) => operations.update(name, id, data),
      delete: (id: string) => operations.delete(name, id),
      getAll: () => operations.getAll(name),
      where: (field: string, op: WhereFilter["op"], value: unknown) =>
        operations.where(name, field, op, value),
      orderBy: (field: string, direction: "asc" | "desc" = "asc") => 
        operations.orderBy(name, field, direction),
      limit: (count: number) => operations.limit(name, count),
      query: (options: QueryOptions) => operations.query(name, options),
      search: (field: string, term: string) => operations.search(name, field, term),
      paginate: (pageSize: number, pageToken?: string) => 
        operations.paginate(name, pageSize, pageToken),
      count: (filters?: WhereFilter[]) => operations.count(name, filters),
      exists: (id: string) => operations.exists(name, id),
    }),
    // Authentication helpers
    setCustomToken: (token: string) => client.setCustomToken(token),
    isAuthenticated: () => client.isAuthenticated(),
    getCurrentUser: () => client.getCurrentUser(),
    signOut: () => client.signOut(),
  };
};

/**
 * Integration helper for LC Backend authentication
 */
export const createLCIntegratedFirestore = (config: {
  projectId: string;
  apiKey: string;
  lcAuthFunction?: () => Promise<string | null>; // Function to get LC auth token
}) => {
  return createFirestore({
    projectId: config.projectId,
    apiKey: config.apiKey,
    ...(config.lcAuthFunction !== undefined && { getAuthToken: config.lcAuthFunction }),
    onTokenRefresh: (token: string) => {
      // Store the refreshed token if needed
      if (typeof window !== "undefined") {
        TokenManager.storeFirebaseTokens({ idToken: token });
      }
    },
  });
};

/**
 * Default export for convenience
 */
export default createFirestore;
