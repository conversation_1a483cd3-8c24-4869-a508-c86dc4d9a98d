import { defineConfig } from "@rslib/core";
import { pluginReact } from "@rsbuild/plugin-react";

export default defineConfig({
  lib: [
    {
      format: "esm",
      syntax: "es2021",
      dts: true,
    },
  ],
  output: {
    target: "web",
    distPath: {
      root: "./dist",
    },
  },
  plugins: [
    pluginReact({
      swcReactOptions: {
        runtime: "automatic",
      },
    }),
  ],
  source: {
    entry: {
      index: "./src/index.ts",
      types: "./src/types.ts",
      hooks: "./src/hooks.ts",
      "client-hooks": "./src/client-hooks.ts",
    },
  },
  tools: {
    rspack: {
      externals: {
        react: "react",
        "react-dom": "react-dom",
      },
    },
  },
});
