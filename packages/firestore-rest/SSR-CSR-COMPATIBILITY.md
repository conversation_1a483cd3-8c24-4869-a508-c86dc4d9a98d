# Next.js SSR/CSR Compatibility Guide

This document explains how the `@gd/firestore-rest` package has been modified to work seamlessly in both Next.js Server-Side Rendering (SSR) and Client-Side Rendering (CSR) environments.

## Changes Made for Compatibility

### 1. Environment Detection Utilities

Added utilities to safely detect browser vs server environments:

```typescript
import { isBrowser, isServer } from "@gd/firestore-rest";

// Safe environment checks
if (isBrowser()) {
  // Browser-specific code
}

if (isServer()) {
  // Server-specific code
}
```

### 2. Universal Storage System

Replaced direct `localStorage` usage with a universal storage system:

- **Browser**: Uses `localStorage` when available
- **Server**: Uses in-memory storage as fallback
- **Graceful degradation**: Handles cases where `localStorage` is not available

```typescript
import { universalStorage } from "@gd/firestore-rest";

// Works in both environments
universalStorage.setItem('key', 'value');
const value = universalStorage.getItem('key');
```

### 3. React Hooks Compatibility

- **Removed** `"use client"` directive from main hooks file
- **Added** separate `client-hooks` export for explicit client-side usage
- **Maintained** full functionality in both environments

### 4. Package Exports Configuration

Updated `package.json` exports to support multiple environments:

```json
{
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "node": "./dist/index.js",
      "edge-light": "./dist/index.js",
      "browser": "./dist/index.js",
      "import": "./dist/index.js",
      "default": "./dist/index.js"
    }
  }
}
```

## Usage Patterns

### Server-Side Rendering (SSR)

```typescript
// pages/api/users.ts or app/api/users/route.ts
import { createFirestore } from "@gd/firestore-rest";

export async function GET() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
  });

  const users = await firestore.collection("users").getAll();
  return Response.json(users);
}
```

### Client-Side Rendering (CSR)

#### Option 1: Universal Hooks (Recommended)

```tsx
// Works in both SSR and CSR
import { useCollection } from "@gd/firestore-rest";

const UsersComponent = () => {
  const { data, loading, error } = useCollection(firestore.operations, "users");
  // Component logic
};
```

#### Option 2: Explicit Client Hooks

```tsx
// For components that must be client-side only
"use client";

import { useCollection } from "@gd/firestore-rest/client-hooks";

const ClientOnlyComponent = () => {
  const { data, loading, error } = useCollection(firestore.operations, "users");
  // Component logic
};
```

### Hybrid SSR + CSR

```tsx
// pages/users.tsx
import { createFirestore, useCollection } from "@gd/firestore-rest";

export async function getServerSideProps() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
  });

  const initialUsers = await firestore.collection("users").getAll();

  return {
    props: { initialUsers },
  };
}

const UsersPage = ({ initialUsers }) => {
  const firestore = createFirestore({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  });

  // This hook will use initialUsers on first render, then fetch fresh data
  const { data: users, loading } = useCollection(
    firestore.operations,
    "users",
    { initialData: initialUsers }
  );

  return (
    <div>
      {users?.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
};
```

## Key Benefits

1. **Seamless Integration**: Works in both SSR and CSR without code changes
2. **Performance**: Server-side data fetching for faster initial page loads
3. **SEO Friendly**: Content is available during server-side rendering
4. **Progressive Enhancement**: Graceful degradation when browser APIs are unavailable
5. **Type Safety**: Full TypeScript support in both environments

## Migration Guide

If you're upgrading from a previous version:

1. **No breaking changes** for basic usage
2. **Optional**: Use `client-hooks` export for explicit client-side components
3. **Recommended**: Leverage SSR capabilities for better performance

## Testing

The package includes automated tests to verify SSR/CSR compatibility:

```bash
cd packages/firestore-rest
pnpm build
node test-ssr-csr.cjs
```

## Troubleshooting

### Common Issues

1. **"window is not defined"**: This should not occur with the updated package
2. **"localStorage is not available"**: The universal storage handles this automatically
3. **Hydration mismatches**: Use `initialData` in hooks to prevent SSR/CSR data differences

### Debug Environment Detection

```typescript
import { isBrowser, isServer } from "@gd/firestore-rest";

console.log('Environment:', {
  isBrowser: isBrowser(),
  isServer: isServer(),
  hasWindow: typeof window !== 'undefined',
  hasLocalStorage: typeof localStorage !== 'undefined'
});
```

This compatibility layer ensures your Firestore operations work seamlessly across all Next.js rendering strategies.
