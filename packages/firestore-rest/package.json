{"name": "@gd/firestore-rest", "version": "0.0.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "edge-light": "./dist/index.js", "browser": "./dist/index.js", "import": "./dist/index.js", "default": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "node": "./dist/types.js", "edge-light": "./dist/types.js", "browser": "./dist/types.js", "import": "./dist/types.js", "default": "./dist/types.js"}, "./hooks": {"types": "./dist/hooks.d.ts", "node": "./dist/hooks.js", "edge-light": "./dist/hooks.js", "browser": "./dist/hooks.js", "import": "./dist/hooks.js", "default": "./dist/hooks.js"}, "./client-hooks": {"types": "./dist/client-hooks.d.ts", "browser": "./dist/client-hooks.js", "import": "./dist/client-hooks.js", "default": "./dist/client-hooks.js"}}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rslib build", "check": "biome check --write", "dev": "rslib build --watch", "format": "biome format --write", "type-check": "tsc --project tsconfig.check.json", "clean": "rm -rf dist", "test-build": "node build-test.js", "build-and-test": "pnpm clean && pnpm build && pnpm test-build"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rsbuild/plugin-react": "^1.3.2", "@rslib/core": "^0.9.2", "@types/react": "^19.1.6", "react": "^19.1.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=19.1.0", "react-dom": ">=19.1.0"}, "private": true, "dependencies": {"@types/node": "^22.15.30", "ofetch": "^1.4.1", "zod": "^3.25.56"}}